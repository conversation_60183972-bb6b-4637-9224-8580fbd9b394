"""Custom exceptions for HLS validator."""


class HLSValidationError(Exception):
    """Base exception for HLS validation errors."""
    pass


class PlaylistLoadError(HLSValidationError):
    """Exception raised when playlist cannot be loaded."""
    pass


class PlaylistParseError(HLSValidationError):
    """Exception raised when playlist cannot be parsed."""
    pass


class SegmentValidationError(HLSValidationError):
    """Exception raised when segment validation fails."""
    pass


class NetworkError(HLSValidationError):
    """Exception raised for network-related errors."""
    pass


class TSAnalysisError(HLSValidationError):
    """Exception raised during TS segment analysis."""
    pass
