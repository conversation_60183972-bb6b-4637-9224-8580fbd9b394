"""Core HLS validation functionality."""

import re
from typing import Dict, List, Optional, Tuple
from urllib.parse import urljoin, urlparse

import m3u8
import requests

from .exceptions import (
    HLSValidationError, PlaylistLoadError, PlaylistParseError,
    SegmentValidationError, NetworkError, TSAnalysisError
)
from .logging_config import LoggingMixin
from .ts_analyzer import TSAnalyzer


class HLSValidator(LoggingMixin):
    """Main HLS stream validator class."""

    def __init__(self, timeout: int = 30):
        """Initialize the HLS validator.

        Args:
            timeout: Request timeout in seconds
        """
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'HLS-Validator/1.0'
        })

        # Initialize TS analyzer
        self.ts_analyzer = TSAnalyzer()
    
    def validate_stream(self, url: str, check_pts: bool = False) -> Dict:
        """Validate an HLS stream.

        Args:
            url: URL to the master playlist
            check_pts: Whether to check for overlapping PTS in audio/video streams

        Returns:
            Dictionary containing validation results
        """
        results = {
            'url': url,
            'valid': True,
            'errors': [],
            'warnings': [],
            'playlists': []
        }
        
        try:
            # Load and parse master playlist
            master_playlist = self._load_playlist(url)
            
            if not master_playlist.is_variant:
                # Single playlist - validate directly
                playlist_result = self._validate_media_playlist(url, master_playlist, check_pts=check_pts)
                results['playlists'].append(playlist_result)
            else:
                # Master playlist with variants
                for playlist in master_playlist.playlists:
                    playlist_url = urljoin(url, playlist.uri)
                    playlist_result = self._validate_media_playlist(playlist_url, None, playlist, check_pts=check_pts)
                    results['playlists'].append(playlist_result)
                
                # Cross-validate playlists
                self._cross_validate_playlists(results['playlists'], results)
            
        except Exception as e:
            results['valid'] = False
            results['errors'].append(f"Failed to validate stream: {str(e)}")
            self.log_error(f"Validation failed for {url}: {e}", exc_info=True)
        
        # Set overall validity
        results['valid'] = len(results['errors']) == 0
        
        return results
    
    def _load_playlist(self, url: str) -> m3u8.M3U8:
        """Load and parse an m3u8 playlist.
        
        Args:
            url: URL to the playlist
            
        Returns:
            Parsed M3U8 object
        """
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            playlist = m3u8.loads(response.text, uri=url)
            return playlist
            
        except requests.RequestException as e:
            raise HLSValidationError(f"Failed to load playlist from {url}: {e}")
        except Exception as e:
            raise HLSValidationError(f"Failed to parse playlist from {url}: {e}")
    
    def _validate_media_playlist(self, url: str, playlist: Optional[m3u8.M3U8] = None,
                                variant_info: Optional[object] = None, check_pts: bool = False) -> Dict:
        """Validate a media playlist.
        
        Args:
            url: URL to the media playlist
            playlist: Pre-loaded playlist object (optional)
            variant_info: Variant information from master playlist
            
        Returns:
            Dictionary containing playlist validation results
        """
        result = {
            'url': url,
            'valid': True,
            'errors': [],
            'warnings': [],
            'segments': [],
            'declared_bandwidth': getattr(getattr(variant_info, 'stream_info', None), 'bandwidth', None) if variant_info else None,
            'declared_resolution': getattr(getattr(variant_info, 'stream_info', None), 'resolution', None) if variant_info else None,
            'segment_durations': []
        }
        
        try:
            if playlist is None:
                playlist = self._load_playlist(url)
            
            # Validate segments
            for i, segment in enumerate(playlist.segments):
                segment_url = urljoin(url, segment.uri)
                segment_result = self._validate_segment(segment_url, segment, i, check_pts=check_pts)
                result['segments'].append(segment_result)
                result['segment_durations'].append(segment.duration)
                
                if not segment_result['valid']:
                    result['valid'] = False
                    result['errors'].extend(segment_result['errors'])
                
                result['warnings'].extend(segment_result['warnings'])
            
            # Check segment duration consistency
            self._check_segment_duration_consistency(result)
            
        except Exception as e:
            result['valid'] = False
            result['errors'].append(f"Failed to validate media playlist: {str(e)}")
            self.logger.error(f"Media playlist validation failed for {url}: {e}")
        
        return result
    
    def _validate_segment(self, url: str, segment_info: object, index: int, check_pts: bool = False) -> Dict:
        """Validate a single TS segment.

        Args:
            url: URL to the segment
            segment_info: Segment information from playlist
            index: Segment index
            check_pts: Whether to check for overlapping PTS in audio/video streams

        Returns:
            Dictionary containing segment validation results
        """
        result = {
            'url': url,
            'index': index,
            'valid': True,
            'errors': [],
            'warnings': [],
            'duration': segment_info.duration,
            'size_bytes': None,
            'starts_with_iframe': None
        }
        
        try:
            # Download segment headers to get size
            response = self.session.head(url, timeout=self.timeout)
            response.raise_for_status()

            content_length = response.headers.get('content-length')
            if content_length:
                result['size_bytes'] = int(content_length)

            # Download first portion of segment for TS analysis
            response = self.session.get(url, timeout=self.timeout,
                                      headers={'Range': 'bytes=0-8191'})  # First 8KB

            if response.status_code in [200, 206]:  # 206 for partial content
                # Use TS analyzer for more accurate I-frame detection
                ts_analysis = self.ts_analyzer.analyze_segment(response.content, check_pts=check_pts)

                result['starts_with_iframe'] = ts_analysis['starts_with_iframe']
                result['ts_analysis'] = {
                    'valid_ts': ts_analysis['valid_ts'],
                    'has_video': ts_analysis['has_video'],
                    'packet_count': ts_analysis['packet_count'],
                    'valid_packets': ts_analysis['valid_packets']
                }

                # Add PTS analysis if requested
                if check_pts and 'pts_analysis' in ts_analysis:
                    result['ts_analysis']['pts_analysis'] = ts_analysis['pts_analysis']

                # Add TS analysis errors and warnings
                result['errors'].extend(ts_analysis['errors'])
                result['warnings'].extend(ts_analysis['warnings'])

                if not result['starts_with_iframe'] and ts_analysis['has_video']:
                    result['errors'].append(f"Segment {index} does not start with I-frame")
                    result['valid'] = False

                if not ts_analysis['valid_ts']:
                    result['errors'].append(f"Segment {index} contains invalid TS packets")
                    result['valid'] = False

            else:
                result['warnings'].append(f"Could not download segment {index} for analysis")

        except Exception as e:
            result['valid'] = False
            result['errors'].append(f"Failed to validate segment {index}: {str(e)}")
            self.logger.error(f"Segment validation failed for {url}: {e}")
        
        return result
    

    
    def _check_segment_duration_consistency(self, playlist_result: Dict):
        """Check if segment durations are consistent.
        
        Args:
            playlist_result: Playlist validation result to update
        """
        durations = playlist_result['segment_durations']
        
        if not durations:
            return
        
        # Check for significant duration variations (>10% difference)
        avg_duration = sum(durations) / len(durations)
        
        for i, duration in enumerate(durations):
            if abs(duration - avg_duration) / avg_duration > 0.1:
                playlist_result['warnings'].append(
                    f"Segment {i} duration ({duration}s) varies significantly from average ({avg_duration:.2f}s)"
                )
    
    def _cross_validate_playlists(self, playlists: List[Dict], results: Dict):
        """Cross-validate multiple playlists for consistency.

        Args:
            playlists: List of playlist validation results
            results: Overall validation results to update
        """
        if len(playlists) < 2:
            return

        # Check segment count consistency
        segment_counts = [len(p['segments']) for p in playlists]
        if len(set(segment_counts)) > 1:
            results['errors'].append(
                f"Inconsistent segment counts across playlists: {segment_counts}"
            )

        # Check total duration consistency
        total_durations = [sum(p['segment_durations']) for p in playlists]
        avg_duration = sum(total_durations) / len(total_durations)

        for i, duration in enumerate(total_durations):
            if abs(duration - avg_duration) / avg_duration > 0.05:  # 5% tolerance
                results['warnings'].append(
                    f"Playlist {i} total duration ({duration:.2f}s) varies from average ({avg_duration:.2f}s)"
                )

        # Cross-validate segment sizes and durations
        self._validate_segment_consistency(playlists, results)

        # Validate declared vs actual bitrates
        self._validate_bitrates(playlists, results)

    def _validate_segment_consistency(self, playlists: List[Dict], results: Dict):
        """Validate that segments have consistent durations across different quality levels.

        Args:
            playlists: List of playlist validation results
            results: Overall validation results to update
        """
        if len(playlists) < 2:
            return

        # Get the minimum number of segments across all playlists
        min_segments = min(len(p['segments']) for p in playlists)

        # Compare segment durations across playlists
        for segment_idx in range(min_segments):
            durations = []
            sizes = []

            for playlist_idx, playlist in enumerate(playlists):
                if segment_idx < len(playlist['segments']):
                    segment = playlist['segments'][segment_idx]
                    durations.append(segment['duration'])
                    if segment['size_bytes']:
                        sizes.append(segment['size_bytes'])

            # Check duration consistency (should be very similar)
            if len(durations) > 1:
                avg_duration = sum(durations) / len(durations)
                for i, duration in enumerate(durations):
                    if abs(duration - avg_duration) / avg_duration > 0.02:  # 2% tolerance
                        results['errors'].append(
                            f"Segment {segment_idx} duration inconsistent across playlists: "
                            f"playlist {i} has {duration:.2f}s vs average {avg_duration:.2f}s"
                        )

            # Check if segment sizes are proportional to declared bitrates
            if len(sizes) > 1 and len(playlists) > 1:
                self._check_size_bitrate_correlation(segment_idx, sizes, playlists, results)

    def _check_size_bitrate_correlation(self, segment_idx: int, sizes: List[int],
                                       playlists: List[Dict], results: Dict):
        """Check if segment sizes correlate with declared bitrates.

        Args:
            segment_idx: Index of the segment being checked
            sizes: List of segment sizes in bytes
            playlists: List of playlist validation results
            results: Overall validation results to update
        """
        # Get declared bitrates for playlists that have size data
        bitrates = []
        valid_sizes = []

        size_idx = 0
        for playlist in playlists:
            if (segment_idx < len(playlist['segments']) and
                playlist['segments'][segment_idx]['size_bytes'] is not None):

                if playlist['declared_bandwidth']:
                    bitrates.append(playlist['declared_bandwidth'])
                    valid_sizes.append(sizes[size_idx])
                size_idx += 1

        if len(bitrates) < 2 or len(valid_sizes) < 2:
            return

        # Calculate expected size ratios based on bitrate ratios
        base_bitrate = bitrates[0]
        base_size = valid_sizes[0]

        for i in range(1, len(bitrates)):
            expected_ratio = bitrates[i] / base_bitrate
            actual_ratio = valid_sizes[i] / base_size

            # Allow 30% tolerance for size/bitrate correlation
            if abs(actual_ratio - expected_ratio) / expected_ratio > 0.3:
                results['warnings'].append(
                    f"Segment {segment_idx} size ratio ({actual_ratio:.2f}) doesn't match "
                    f"bitrate ratio ({expected_ratio:.2f}) - possible encoding issue"
                )

    def _validate_bitrates(self, playlists: List[Dict], results: Dict):
        """Validate declared bitrates against actual segment sizes.

        Args:
            playlists: List of playlist validation results
            results: Overall validation results to update
        """
        for i, playlist in enumerate(playlists):
            if not playlist['declared_bandwidth']:
                continue

            # Calculate average actual bitrate from segment sizes
            total_size_bits = 0
            total_duration = 0
            valid_segments = 0

            for segment in playlist['segments']:
                if segment['size_bytes'] is not None:
                    total_size_bits += segment['size_bytes'] * 8  # Convert to bits
                    total_duration += segment['duration']
                    valid_segments += 1

            if valid_segments == 0 or total_duration == 0:
                continue

            actual_bitrate = total_size_bits / total_duration
            declared_bitrate = playlist['declared_bandwidth']

            # Allow 20% tolerance between declared and actual bitrate
            if abs(actual_bitrate - declared_bitrate) / declared_bitrate > 0.2:
                results['warnings'].append(
                    f"Playlist {i+1} actual bitrate ({actual_bitrate:.0f} bps) differs "
                    f"significantly from declared ({declared_bitrate} bps)"
                )
