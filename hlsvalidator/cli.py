"""Command-line interface for HLS validator."""

import json
import sys
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text

from .logging_config import setup_logging
from .validator import HLSValidator


app = typer.Typer(
    name="hlsvalidator",
    help="Validate HLS (HTTP Live Streaming) streams for compliance and quality.",
    add_completion=False
)

console = Console()





@app.command()
def validate(
    url: Optional[str] = typer.Argument(None, help="URL to the HLS master playlist or media playlist"),
    timeout: int = typer.Option(30, "--timeout", "-t", help="Request timeout in seconds"),
    output_format: str = typer.Option("table", "--format", "-f",
                                     help="Output format: table, json, or summary"),
    output_file: Optional[Path] = typer.Option(None, "--output", "-o",
                                              help="Output file path (optional)"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose logging"),
    no_color: bool = typer.Option(False, "--no-color", help="Disable colored output"),
    url_list: Optional[Path] = typer.Option(None, "--list", "-l",
                                           help="File containing list of URLs to validate (one per line)"),
    check_pts: bool = typer.Option(False, "--check-pts", help="Check for overlapping PTS (Presentation Time Stamps) in audio/video streams")
):
    """Validate an HLS stream for compliance and quality issues."""
    
    # Setup logging
    log_level = "DEBUG" if verbose else "INFO"
    setup_logging(level=log_level)
    
    # Disable colors if requested
    if no_color:
        console._color_system = None
    
    # Check that either URL or list file is provided
    if not url and not url_list:
        console.print("[red]Error: Either provide a URL or use --list to specify a file with URLs[/red]")
        raise typer.Exit(1)

    if url and url_list:
        console.print("[red]Error: Cannot use both URL argument and --list option simultaneously[/red]")
        raise typer.Exit(1)

    # Create validator
    validator = HLSValidator(timeout=timeout)

    if url_list:
        # Validate multiple URLs from file
        validate_url_list(validator, url_list, output_format, output_file, verbose, check_pts)
    else:
        # Validate single URL
        if url is None:
            console.print("[red]Error: URL is required when not using --list[/red]")
            raise typer.Exit(1)
        validate_single_url(validator, url, output_format, output_file, verbose, check_pts)


def validate_single_url(validator, url: str, output_format: str, output_file: Optional[Path], verbose: bool, check_pts: bool = False):
    """Validate a single URL."""
    # Validate URL format
    if not url.startswith(('http://', 'https://')):
        console.print("[red]Error: URL must start with http:// or https://[/red]")
        raise typer.Exit(1)

    with console.status("[bold green]Validating HLS stream..."):
        results = validator.validate_stream(url, check_pts=check_pts)

    # Output results
    if output_format == "json":
        output_json(results, output_file)
    elif output_format == "summary":
        output_summary(results, output_file)
    else:
        output_table(results, output_file, verbose=verbose)

    # Exit with error code if validation failed
    if not results['valid']:
        raise typer.Exit(1)


def validate_url_list(validator, url_list_file: Path, output_format: str, output_file: Optional[Path], verbose: bool, check_pts: bool = False):
    """Validate multiple URLs from a file."""
    try:
        with open(url_list_file, 'r') as f:
            urls = [line.strip() for line in f if line.strip() and not line.strip().startswith('#')]
    except FileNotFoundError:
        console.print(f"[red]Error: File {url_list_file} not found[/red]")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Error reading file {url_list_file}: {e}[/red]")
        raise typer.Exit(1)

    if not urls:
        console.print("[red]Error: No valid URLs found in the file[/red]")
        raise typer.Exit(1)

    console.print(f"[blue]Found {len(urls)} URLs to validate[/blue]")

    all_results = []
    failed_count = 0

    for i, url in enumerate(urls, 1):
        # Skip invalid URLs
        if not url.startswith(('http://', 'https://')):
            console.print(f"[yellow]Skipping invalid URL {i}: {url}[/yellow]")
            continue

        console.print(f"\n[blue]Validating {i}/{len(urls)}: {url}[/blue]")

        try:
            with console.status(f"[bold green]Validating stream {i}/{len(urls)}..."):
                results = validator.validate_stream(url, check_pts=check_pts)

            all_results.append(results)

            # Show brief result for each URL
            status = "✅ VALID" if results['valid'] else "❌ INVALID"
            error_count = len(results.get('errors', []))
            warning_count = len(results.get('warnings', []))

            # Count playlist warnings too
            for playlist in results.get('playlists', []):
                warning_count += len(playlist.get('warnings', []))

            console.print(f"  {status} - Errors: {error_count}, Warnings: {warning_count}")

            if not results['valid']:
                failed_count += 1
                if results.get('errors'):
                    for error in results['errors'][:2]:  # Show first 2 errors
                        console.print(f"    [red]• {error}[/red]")

        except Exception as e:
            console.print(f"  [red]❌ ERROR: {e}[/red]")
            failed_count += 1

    # Output summary
    console.print(f"\n[blue]Validation Summary:[/blue]")
    console.print(f"  Total URLs: {len(urls)}")
    console.print(f"  Valid: {len(all_results) - failed_count}")
    console.print(f"  Invalid: {failed_count}")

    # Output detailed results if requested
    if output_format == "json":
        output_json({"results": all_results, "summary": {"total": len(urls), "valid": len(all_results) - failed_count, "invalid": failed_count}}, output_file)
    elif output_format == "summary":
        # For summary format, output each result
        for result in all_results:
            output_summary(result, None)  # Don't write to file for individual results
            console.print("-" * 80)
    # For table format, we already showed the results above

    # Exit with error code if any validation failed
    if failed_count > 0:
        raise typer.Exit(1)


def output_json(results: dict, output_file: Optional[Path] = None):
    """Output results in JSON format."""
    json_output = json.dumps(results, indent=2, default=str)
    
    if output_file:
        output_file.write_text(json_output)
        console.print(f"[green]Results saved to {output_file}[/green]")
    else:
        console.print(json_output)


def output_summary(results: dict, output_file: Optional[Path] = None):
    """Output results in summary format."""
    lines = []
    
    # Overall status
    status = "✅ VALID" if results['valid'] else "❌ INVALID"
    lines.append(f"Stream Status: {status}")
    lines.append(f"URL: {results['url']}")
    lines.append("")
    
    # Error summary
    if results['errors']:
        lines.append("ERRORS:")
        for error in results['errors']:
            lines.append(f"  • {error}")
        lines.append("")
    
    # Warning summary
    all_warnings = results['warnings'][:]  # Overall warnings

    # Add playlist warnings
    for i, playlist in enumerate(results['playlists']):
        for warning in playlist['warnings']:
            all_warnings.append(f"Playlist {i+1}: {warning}")

    if all_warnings:
        lines.append("WARNINGS:")
        for warning in all_warnings:
            lines.append(f"  • {warning}")
        lines.append("")
    
    # Playlist summary
    lines.append(f"Playlists validated: {len(results['playlists'])}")
    
    for i, playlist in enumerate(results['playlists']):
        status = "✅" if playlist['valid'] else "❌"
        lines.append(f"  {status} Playlist {i+1}: {len(playlist['segments'])} segments")
        
        if playlist['declared_bandwidth']:
            lines.append(f"    Bandwidth: {playlist['declared_bandwidth']} bps")
        if playlist['declared_resolution']:
            lines.append(f"    Resolution: {playlist['declared_resolution']}")
    
    summary_text = "\n".join(lines)
    
    if output_file:
        output_file.write_text(summary_text)
        console.print(f"[green]Results saved to {output_file}[/green]")
    else:
        console.print(summary_text)


def output_table(results: dict, output_file: Optional[Path] = None, verbose: bool = False):
    """Output results in table format."""
    # Overall status panel
    status_color = "green" if results['valid'] else "red"
    status_text = "VALID ✅" if results['valid'] else "INVALID ❌"
    
    status_panel = Panel(
        f"[bold {status_color}]{status_text}[/bold {status_color}]\n"
        f"URL: {results['url']}",
        title="HLS Stream Validation",
        border_style=status_color
    )
    console.print(status_panel)
    
    # Errors table
    if results['errors']:
        error_table = Table(title="Errors", show_header=False, border_style="red")
        error_table.add_column("Error", style="red")
        
        for error in results['errors']:
            error_table.add_row(f"❌ {error}")
        
        console.print(error_table)
    
    # Overall warnings table (always show these as they're cross-playlist issues)
    if results['warnings']:
        warning_table = Table(title="Warnings", show_header=False, border_style="yellow")
        warning_table.add_column("Warning", style="yellow")

        for warning in results['warnings']:
            warning_table.add_row(f"⚠️  {warning}")

        console.print(warning_table)
    
    # Playlists table
    if results['playlists']:
        playlist_table = Table(title="Playlists", border_style="blue")
        playlist_table.add_column("Index", justify="center")
        playlist_table.add_column("Status", justify="center")
        playlist_table.add_column("Segments", justify="center")
        playlist_table.add_column("Bandwidth", justify="right")
        playlist_table.add_column("Resolution", justify="center")
        playlist_table.add_column("Errors", justify="center")
        playlist_table.add_column("Warnings", justify="center")
        
        for i, playlist in enumerate(results['playlists']):
            status = "[green]✅[/green]" if playlist['valid'] else "[red]❌[/red]"
            bandwidth = str(playlist['declared_bandwidth']) if playlist['declared_bandwidth'] else "N/A"
            resolution = str(playlist['declared_resolution']) if playlist['declared_resolution'] else "N/A"
            
            playlist_table.add_row(
                str(i + 1),
                status,
                str(len(playlist['segments'])),
                bandwidth,
                resolution,
                str(len(playlist['errors'])),
                str(len(playlist['warnings']))
            )
        
        console.print(playlist_table)

        # Show all warnings in verbose mode (both overall and playlist-specific)
        if verbose:
            # Collect all warnings
            all_warnings = []

            # Add overall warnings (like size ratio mismatches)
            for warning in results['warnings']:
                all_warnings.append(("Overall", warning))

            # Add playlist-specific warnings
            for i, playlist in enumerate(results['playlists']):
                for warning in playlist['warnings']:
                    all_warnings.append((f"Playlist {i+1}", warning))

            # Display all warnings in a single table
            if all_warnings:
                warning_table = Table(
                    title="All Warnings",
                    border_style="yellow"
                )
                warning_table.add_column("Source", style="cyan", width=12)
                warning_table.add_column("Warning", style="yellow")

                for source, warning in all_warnings:
                    warning_table.add_row(source, f"⚠️  {warning}")

                console.print(warning_table)

        # Detailed segment information for first playlist (if verbose)
        if verbose and results['playlists'] and len(results['playlists'][0]['segments']) <= 20:
            first_playlist = results['playlists'][0]
            if first_playlist['segments']:
                segment_table = Table(title="Segment Details (First Playlist)", border_style="cyan")
                segment_table.add_column("Index", justify="center")
                segment_table.add_column("Duration", justify="right")
                segment_table.add_column("Size (bytes)", justify="right")
                segment_table.add_column("I-Frame", justify="center")
                segment_table.add_column("Status", justify="center")
                
                for segment in first_playlist['segments'][:10]:  # Show first 10 segments
                    status = "[green]✅[/green]" if segment['valid'] else "[red]❌[/red]"
                    size = str(segment['size_bytes']) if segment['size_bytes'] else "N/A"
                    iframe = "[green]✅[/green]" if segment['starts_with_iframe'] else "[red]❌[/red]"
                    
                    segment_table.add_row(
                        str(segment['index']),
                        f"{segment['duration']:.2f}s",
                        size,
                        iframe,
                        status
                    )
                
                if len(first_playlist['segments']) > 10:
                    segment_table.add_row("...", "...", "...", "...", "...")
                
                console.print(segment_table)
    
    # Save to file if requested
    if output_file:
        # For table format, we'll save a simplified text version
        with console.capture() as capture:
            # Re-render without colors for file output
            console._color_system = None
            output_table(results, verbose=verbose)

        output_file.write_text(capture.get())
        console.print(f"[green]Results saved to {output_file}[/green]")


if __name__ == "__main__":
    app()
