"""MPEG-TS segment analysis functionality using PyAV (libav)."""

import io
from typing import Dict

import av

from .exceptions import TSAnalysisError
from .logging_config import LoggingMixin


class TSAnalyzer(LoggingMixin):
    """PyAV-based analyzer for MPEG-TS segments."""

    def __init__(self):
        """Initialize the TS analyzer."""
        self._check_av_availability()

    def _check_av_availability(self):
        """Check if PyAV is properly installed."""
        try:
            # Test basic PyAV functionality
            av.open(io.BytesIO(b''), 'r', format='mpegts')
        except Exception:
            # This is expected to fail with empty data, but ensures PyAV is working
            pass

    def analyze_segment(self, data: bytes, check_pts: bool = False) -> Dict:
        """Analyze a TS segment using PyAV.

        Args:
            data: TS segment data
            check_pts: Whether to check for overlapping PTS in audio/video streams

        Returns:
            Analysis results dictionary
        """
        result = {
            'valid_ts': False,
            'packet_count': 0,
            'valid_packets': 0,
            'has_video': False,
            'has_audio': False,
            'starts_with_iframe': False,
            'video_streams': [],
            'audio_streams': [],
            'duration': None,
            'bitrate': None,
            'errors': [],
            'warnings': []
        }

        if len(data) < 188:
            result['errors'].append("Segment too small to contain TS packets")
            return result

        # Analyze using PyAV directly from memory
        try:
            # Create a BytesIO buffer from the data
            buffer = io.BytesIO(data)

            # Open the buffer as an MPEG-TS container
            with av.open(buffer, 'r', format='mpegts') as container:
                self._analyze_with_pyav(container, result)

                # Check for I-frame at the beginning
                self._check_iframe_start_pyav(container, result)

                # Check for overlapping PTS if requested
                if check_pts:
                    pts_analysis = self._check_pts_overlaps(container)
                    result['pts_analysis'] = pts_analysis

                    # Add warnings for PTS issues
                    if pts_analysis['overlapping_video_pts']:
                        result['warnings'].append(f"Found {len(pts_analysis['overlapping_video_pts'])} overlapping video PTS")
                    if pts_analysis['overlapping_audio_pts']:
                        result['warnings'].append(f"Found {len(pts_analysis['overlapping_audio_pts'])} overlapping audio PTS")
                    if pts_analysis['pts_gaps']:
                        result['warnings'].append(f"Found {len(pts_analysis['pts_gaps'])} PTS gaps")
                    if pts_analysis['av_sync_issues']:
                        max_drift_ms = max(abs(issue['drift_ms']) for issue in pts_analysis['av_sync_issues'])
                        result['warnings'].append(f"Found {len(pts_analysis['av_sync_issues'])} audio/video sync issues (max drift: {max_drift_ms:.1f}ms)")
                    if 'max_av_drift_ms' in pts_analysis and pts_analysis['max_av_drift_ms'] > 16.7:  # More than one frame at 60fps
                        result['warnings'].append(f"Audio/video PTS drift detected (max: {pts_analysis['max_av_drift_ms']:.1f}ms, avg: {pts_analysis.get('avg_av_drift_ms', 0):.1f}ms)")

        except av.FFmpegError as e:
            result['errors'].append(f"PyAV analysis failed: {str(e)}")
            self.log_error(f"PyAV analysis failed: {e}")
        except Exception as e:
            result['errors'].append(f"Failed to analyze segment: {str(e)}")
            self.log_error(f"Segment analysis failed: {e}", exc_info=True)

        return result

    def _analyze_with_pyav(self, container: av.container.InputContainer, result: Dict):
        """Analyze TS container with PyAV to get stream information.

        Args:
            container: PyAV input container
            result: Result dictionary to update
        """
        try:
            # Check if container has streams
            if not container.streams:
                result['errors'].append("No streams found in TS segment")
                return

            result['valid_ts'] = True

            # Estimate packet count from data size
            if hasattr(container, 'size') and container.size:
                result['packet_count'] = container.size // 188
                result['valid_packets'] = result['packet_count']

            # Analyze each stream
            for stream in container.streams:
                if stream.type == 'video':
                    result['has_video'] = True

                    # Get frame rate
                    fps = None
                    if stream.average_rate:
                        fps = float(stream.average_rate)
                    elif stream.base_rate:
                        fps = float(stream.base_rate)

                    video_info = {
                        'codec_name': stream.codec_context.name if stream.codec_context else 'unknown',
                        'width': stream.codec_context.width if stream.codec_context else None,
                        'height': stream.codec_context.height if stream.codec_context else None,
                        'fps': fps,
                        'bitrate': stream.codec_context.bit_rate if stream.codec_context else None,
                        'duration': float(stream.duration * stream.time_base) if stream.duration else None
                    }
                    result['video_streams'].append(video_info)

                elif stream.type == 'audio':
                    result['has_audio'] = True

                    audio_info = {
                        'codec_name': stream.codec_context.name if stream.codec_context else 'unknown',
                        'sample_rate': stream.codec_context.sample_rate if stream.codec_context else None,
                        'channels': stream.codec_context.channels if stream.codec_context else None,
                        'bitrate': stream.codec_context.bit_rate if stream.codec_context else None,
                        'duration': float(stream.duration * stream.time_base) if stream.duration else None
                    }
                    result['audio_streams'].append(audio_info)

            # Get container duration and bitrate
            if container.duration:
                result['duration'] = float(container.duration) / av.time_base

            if container.bit_rate:
                result['bitrate'] = container.bit_rate

        except Exception as e:
            error_msg = f"Unexpected error during PyAV analysis: {str(e)}"
            result['errors'].append(error_msg)
            self.log_error(error_msg, exc_info=True)

    def _check_iframe_start_pyav(self, container: av.container.InputContainer, result: Dict):
        """Check if the TS segment starts with an I-frame using PyAV.

        Args:
            container: PyAV input container
            result: Result dictionary to update
        """
        if not result['has_video']:
            return

        try:
            # Find the first video stream
            video_stream = None
            for stream in container.streams:
                if stream.type == 'video':
                    video_stream = stream
                    break

            if not video_stream:
                result['warnings'].append("No video stream found for I-frame analysis")
                return

            # Reset container to beginning
            container.seek(0)

            # Read the first few packets to find the first video frame
            frame_count = 0
            max_frames_to_check = 5

            for packet in container.demux(video_stream):
                for frame in packet.decode():
                    frame_count += 1

                    if frame_count == 1:  # First frame
                        # Check if it's a key frame (I-frame)
                        is_key_frame = frame.key_frame

                        # Handle pict_type - it can be an integer or enum
                        pict_type = 'UNKNOWN'
                        if hasattr(frame.pict_type, 'name'):
                            pict_type = frame.pict_type.name
                        elif isinstance(frame.pict_type, int):
                            # Map common pict_type values
                            pict_type_map = {1: 'I', 2: 'P', 3: 'B'}
                            pict_type = pict_type_map.get(frame.pict_type, f'TYPE_{frame.pict_type}')

                        # I-frame should be a key frame
                        result['starts_with_iframe'] = is_key_frame and pict_type == 'I'

                        if not result['starts_with_iframe']:
                            result['warnings'].append(
                                f"Segment does not start with I-frame (first frame: "
                                f"key_frame={is_key_frame}, pict_type={pict_type})"
                            )
                        return  # We only need to check the first frame

                    if frame_count >= max_frames_to_check:
                        break

                if frame_count >= max_frames_to_check:
                    break

            if frame_count == 0:
                result['warnings'].append("No video frames found for I-frame analysis")

        except av.FFmpegError as e:
            result['warnings'].append(f"PyAV error during I-frame analysis: {str(e)}")
            self.log_warning(f"PyAV I-frame analysis error: {e}")
        except Exception as e:
            result['warnings'].append(f"Unexpected error during I-frame analysis: {e}")
            self.log_error(f"I-frame analysis error: {e}", exc_info=True)

    def _check_pts_overlaps(self, container: av.container.InputContainer) -> Dict:
        """Check for overlapping PTS (Presentation Time Stamps) in audio and video streams.

        Args:
            container: PyAV container

        Returns:
            Dict containing PTS analysis results
        """
        pts_analysis = {
            'video_pts': [],
            'audio_pts': [],
            'overlapping_video_pts': [],
            'overlapping_audio_pts': [],
            'pts_gaps': [],
            'video_pts_range': None,
            'audio_pts_range': None,
            'av_sync_issues': [],
            'max_av_drift': 0,
            'avg_av_drift': 0
        }

        try:
            # Collect PTS values for each stream type
            video_pts = []
            audio_pts = []

            # Reset container to beginning
            container.seek(0)

            for packet in container.demux():
                if packet.stream.type == 'video':
                    if packet.pts is not None:
                        video_pts.append(packet.pts)
                elif packet.stream.type == 'audio':
                    if packet.pts is not None:
                        audio_pts.append(packet.pts)

            # Sort PTS values
            video_pts.sort()
            audio_pts.sort()

            pts_analysis['video_pts'] = video_pts[:50]  # Limit to first 50 for performance
            pts_analysis['audio_pts'] = audio_pts[:50]  # Limit to first 50 for performance

            # Check for overlapping video PTS
            if len(video_pts) > 1:
                overlapping_video = []
                for i in range(1, min(len(video_pts), 100)):  # Check first 100 packets
                    if video_pts[i] <= video_pts[i-1]:
                        overlapping_video.append({
                            'index': i,
                            'current_pts': video_pts[i],
                            'previous_pts': video_pts[i-1]
                        })
                pts_analysis['overlapping_video_pts'] = overlapping_video

                # Calculate video PTS range
                if video_pts:
                    pts_analysis['video_pts_range'] = {
                        'min': min(video_pts),
                        'max': max(video_pts),
                        'span': max(video_pts) - min(video_pts)
                    }

            # Check for overlapping audio PTS
            if len(audio_pts) > 1:
                overlapping_audio = []
                for i in range(1, min(len(audio_pts), 100)):  # Check first 100 packets
                    if audio_pts[i] <= audio_pts[i-1]:
                        overlapping_audio.append({
                            'index': i,
                            'current_pts': audio_pts[i],
                            'previous_pts': audio_pts[i-1]
                        })
                pts_analysis['overlapping_audio_pts'] = overlapping_audio

                # Calculate audio PTS range
                if audio_pts:
                    pts_analysis['audio_pts_range'] = {
                        'min': min(audio_pts),
                        'max': max(audio_pts),
                        'span': max(audio_pts) - min(audio_pts)
                    }

            # Check for significant PTS gaps (gaps larger than expected frame duration)
            pts_gaps = []

            # Check video PTS gaps
            if len(video_pts) > 1:
                expected_frame_duration = 3000  # Approximate PTS units for 30fps (90000/30)
                for i in range(1, min(len(video_pts), 50)):  # Check first 50 gaps
                    gap = video_pts[i] - video_pts[i-1]
                    if gap > expected_frame_duration * 3:  # Gap larger than 3 frames
                        pts_gaps.append({
                            'stream_type': 'video',
                            'gap_start': video_pts[i-1],
                            'gap_end': video_pts[i],
                            'gap_size': gap
                        })

            # Check audio PTS gaps
            if len(audio_pts) > 1:
                expected_audio_duration = 1920  # Approximate PTS units for audio frame (90000/46.875)
                for i in range(1, min(len(audio_pts), 50)):  # Check first 50 gaps
                    gap = audio_pts[i] - audio_pts[i-1]
                    if gap > expected_audio_duration * 5:  # Gap larger than 5 audio frames
                        pts_gaps.append({
                            'stream_type': 'audio',
                            'gap_start': audio_pts[i-1],
                            'gap_end': audio_pts[i],
                            'gap_size': gap
                        })

            pts_analysis['pts_gaps'] = pts_gaps

            # Check for audio/video PTS alignment issues
            if video_pts and audio_pts:
                av_sync_issues = []
                drift_values = []

                # Compare audio and video PTS at similar time points
                # We'll check alignment by finding the closest audio PTS for each video PTS
                for i, v_pts in enumerate(video_pts[:20]):  # Check first 20 video frames
                    # Find closest audio PTS
                    closest_audio_pts = None
                    min_diff = float('inf')

                    for a_pts in audio_pts:
                        diff = abs(a_pts - v_pts)
                        if diff < min_diff:
                            min_diff = diff
                            closest_audio_pts = a_pts

                    if closest_audio_pts is not None:
                        drift = closest_audio_pts - v_pts
                        drift_values.append(drift)

                        # Flag significant drift (more than ~33ms at 90kHz timebase)
                        # 90000 Hz timebase: 33ms = 2970 units
                        if abs(drift) > 2970:
                            av_sync_issues.append({
                                'video_pts': v_pts,
                                'audio_pts': closest_audio_pts,
                                'drift': drift,
                                'drift_ms': drift / 90.0,  # Convert to milliseconds
                                'frame_index': i
                            })

                pts_analysis['av_sync_issues'] = av_sync_issues

                if drift_values:
                    pts_analysis['max_av_drift'] = max(abs(d) for d in drift_values)
                    pts_analysis['avg_av_drift'] = sum(abs(d) for d in drift_values) / len(drift_values)

                    # Convert to milliseconds for easier interpretation
                    pts_analysis['max_av_drift_ms'] = pts_analysis['max_av_drift'] / 90.0
                    pts_analysis['avg_av_drift_ms'] = pts_analysis['avg_av_drift'] / 90.0

        except Exception as e:
            self.log_error(f"PTS analysis failed: {e}", exc_info=True)
            pts_analysis['error'] = str(e)

        return pts_analysis
