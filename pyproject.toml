[project]
name = "hlsvalidator"
version = "0.1.0"
description = "A CLI tool to validate HLS streams"
authors = [
    {name = "User"}
]
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "typer (>=0.16.0,<0.17.0)",
    "m3u8 (>=6.0.0,<7.0.0)",
    "requests (>=2.32.4,<3.0.0)",
    "av (>=15.0.0,<16.0.0)",
]

[project.scripts]
hlsvalidator = "hlsvalidator.cli:app"


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"
pytest-cov = "^6.2.1"
black = "^25.1.0"
isort = "^6.0.1"
