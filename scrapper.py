# curl 'https://mam-preprod-001.hexaglobe.net/MAM?crudAction=detail&crudControllerFqcn=App%5CController%5CAdmin%5CVideoAssets%5CVideoAssetsCrudController&entityId=96905' \
#   -H 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \
#   -H 'accept-language: en-US,en;q=0.9,fr;q=0.8' \
#   -H 'cache-control: no-cache' \
#   -b 'userid=48d47917-dd8f-4e4c-92c6-5be3a1398a88.ZkNyDA.i2fh2dmb-JlBqMQBkp3gAaneMcw; device_view=full; PHPSESSID=04581ae3ab614f2ba8e8e3379de85e3d; ea_color_scheme=dark' \
#   -H 'pragma: no-cache' \
#   -H 'priority: u=0, i' \
#   -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
#   -H 'sec-ch-ua-mobile: ?0' \
#   -H 'sec-ch-ua-platform: "Linux"' \
#   -H 'sec-fetch-dest: document' \
#   -H 'sec-fetch-mode: navigate' \
#   -H 'sec-fetch-site: same-origin' \
#   -H 'sec-fetch-user: ?1' \
#   -H 'upgrade-insecure-requests: 1' \
#   -H 'user-agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'

import requests
from bs4 import BeautifulSoup
import re
import json
from urllib.parse import urljoin, urlparse


base_asset_url = "https://cned-mam-p.hexaglobe.net/MAM?crudAction=detail&crudControllerFqcn=App%5CController%5CAdmin%5CVideoAssets%5CVideoAssetsCrudController&entityId="


# curl 'https://cned-mam-p.hexaglobe.net/login' \
#   -H 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \
#   -H 'accept-language: en-US,en;q=0.9,fr;q=0.8' \
#   -H 'cache-control: no-cache' \
#   -H 'content-type: application/x-www-form-urlencoded' \
#   -b 'userid=48d47917-dd8f-4e4c-92c6-5be3a1398a88.ZkNyDA.i2fh2dmb-JlBqMQBkp3gAaneMcw; device_view=full; ea_color_scheme=dark; PHPSESSID=59c42cdc252d97ce6550d7f8dff99263' \
#   -H 'origin: https://cned-mam-p.hexaglobe.net' \
#   -H 'pragma: no-cache' \
#   -H 'priority: u=0, i' \
#   -H 'referer: https://cned-mam-p.hexaglobe.net/login' \
#   -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
#   -H 'sec-ch-ua-mobile: ?0' \
#   -H 'sec-ch-ua-platform: "Linux"' \
#   -H 'sec-fetch-dest: document' \
#   -H 'sec-fetch-mode: navigate' \
#   -H 'sec-fetch-site: same-origin' \
#   -H 'sec-fetch-user: ?1' \
#   -H 'upgrade-insecure-requests: 1' \
#   -H 'user-agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
#   --data-raw '_username=admin%40hexaglobe.com&_password=7l9T3x0%3FS6WHeFyFnuc6m%3DhmtABFeOYr1N&_csrf_token=4b0f9b6864.Ce5SiKCBTGdgtLnZNxC6jGwniEMHn-6Frq1FFlffiZ0.WJ0b39nIKVEujd63dmGKxiNxvhw-5qLozN8peS7r6udAhBfZ2uQbBVjH9A'

login_url = "https://cned-mam-p.hexaglobe.net/login"


login_data = {
    "_username": "<EMAIL>",
    "_password": "7l9T3x0?S6WHeFyFnuc6m=hmtABFeOYr1N",
}


def extract_csrf_token(soup):
    """Extract CSRF token from the page."""
    csrf_input = soup.find('input', {'name': '_csrf_token'})
    if csrf_input:
        return csrf_input.get('value')
    return None


def find_hls_sharing_links(session, base_url, entity_id):
    """Find HLS sharing links from the Partager tab."""

    # First, get the main asset page to extract any necessary tokens
    main_response = session.get(base_url)
    if main_response.status_code != 200:
        print(f"Failed to access main page: {main_response.status_code}")
        return []

    main_soup = BeautifulSoup(main_response.content, "html.parser")

    # Look for sharing/partager tab or sharing functionality
    # This might be loaded via AJAX or be part of the main page

    hls_links = []

    # Method 1: Look for HLS links directly in the main page
    # Search for URLs containing 'master.m3u8' or HLS-related patterns
    for link in main_soup.find_all(['a', 'input', 'span', 'div'], href=True):
        href = link.get('href', '')
        if 'master.m3u8' in href or '.m3u8' in href:
            hls_links.append(href)

    # Method 2: Look for HLS links in text content
    page_text = main_soup.get_text()
    import re
    hls_pattern = r'https?://[^\s]+\.m3u8[^\s]*'
    found_links = re.findall(hls_pattern, page_text)
    hls_links.extend(found_links)

    # Method 3: Look for sharing tab or modal
    # Check for elements that might contain sharing functionality
    sharing_elements = main_soup.find_all(['div', 'section', 'tab'],
                                        class_=re.compile(r'(share|partag|sharing)', re.I))

    for element in sharing_elements:
        element_text = element.get_text()
        found_links = re.findall(hls_pattern, element_text)
        hls_links.extend(found_links)

    # Method 4: Look for JavaScript variables or AJAX endpoints
    scripts = main_soup.find_all('script')
    for script in scripts:
        if script.string:
            found_links = re.findall(hls_pattern, script.string)
            hls_links.extend(found_links)

    # Method 5: Try to find sharing API endpoint
    # Look for forms or AJAX calls that might lead to sharing functionality
    forms = main_soup.find_all('form')
    for form in forms:
        action = form.get('action', '')
        if 'share' in action.lower() or 'partag' in action.lower():
            # Try to submit the form to get sharing links
            try:
                form_data = {}
                for input_elem in form.find_all('input'):
                    name = input_elem.get('name')
                    value = input_elem.get('value', '')
                    if name:
                        form_data[name] = value

                if action.startswith('/'):
                    action = urljoin(base_url, action)

                share_response = session.post(action, data=form_data)
                if share_response.status_code == 200:
                    share_soup = BeautifulSoup(share_response.content, "html.parser")
                    share_text = share_soup.get_text()
                    found_links = re.findall(hls_pattern, share_text)
                    hls_links.extend(found_links)
            except Exception as e:
                print(f"Error trying to access sharing form: {e}")

    # Remove duplicates and clean up links
    unique_links = []
    for link in set(hls_links):
        # Clean up the link (remove trailing quotes, commas, etc.)
        cleaned_link = link.strip().rstrip("',\"").strip()
        if cleaned_link and cleaned_link.startswith('http'):
            unique_links.append(cleaned_link)

    return unique_links


def scrape_hls_links(asset_url, login_url, login_data):
    """Main function to scrape HLS sharing links from an asset."""

    with requests.Session() as session:
        # Set headers to mimic a real browser
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,fr;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

        # First, get the login page to extract CSRF token if needed
        login_page = session.get(login_url)
        if login_page.status_code == 200:
            login_soup = BeautifulSoup(login_page.content, "html.parser")
            csrf_token = extract_csrf_token(login_soup)
            if csrf_token:
                login_data['_csrf_token'] = csrf_token

        # Perform login
        print("Logging in...")
        post_response = session.post(login_url, data=login_data)
        print(f"Login status: {post_response.status_code}")

        if post_response.status_code not in [200, 302]:
            print("Login failed!")
            return []

        # Extract entity ID from asset URL
        entity_id = None
        if 'entityId=' in asset_url:
            entity_id = asset_url.split('entityId=')[1].split('&')[0]

        print(f"Accessing asset page (Entity ID: {entity_id})...")

        # Find HLS sharing links
        hls_links = find_hls_sharing_links(session, asset_url, entity_id)

        return hls_links


def get_interesting_hls_link(hls_links):
    """Get the interesting HLS link (the one with cned-mam-p.hexaglobe.net)."""
    for link in hls_links:
        if 'cned-mam-p.hexaglobe.net' in link:
            return link
    return None


def scrape_asset_range(start_id=100000, end_id=100316):
    """Scrape HLS links from a range of asset IDs."""

    found_links = []

    # Create session once for all requests
    with requests.Session() as session:
        # Set headers to mimic a real browser
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,fr;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

        # Login once
        login_page = session.get(login_url)
        if login_page.status_code == 200:
            login_soup = BeautifulSoup(login_page.content, "html.parser")
            csrf_token = extract_csrf_token(login_soup)
            if csrf_token:
                login_data['_csrf_token'] = csrf_token

        login_response = session.post(login_url, data=login_data)
        if login_response.status_code not in [200, 302]:
            print("Login failed!")
            return []

        # Loop through all asset IDs from start_id to end_id
        for current_id in range(start_id, end_id + 1):
            asset_url = base_asset_url + str(current_id)

            try:
                # Get the asset page
                response = session.get(asset_url)

                if response.status_code == 200:
                    # Look for HLS links
                    hls_links = find_hls_sharing_links(session, asset_url, str(current_id))

                    # Get the interesting link (cned-mam-p.hexaglobe.net one)
                    interesting_link = get_interesting_hls_link(hls_links)

                    if interesting_link:
                        print(interesting_link)
                        found_links.append(interesting_link)

            except Exception:
                # Silently continue on errors
                pass

    return found_links


# Main execution
if __name__ == "__main__":
    # Scrape all asset IDs from 100000 to 100316
    scrape_asset_range(start_id=100000, end_id=100316)
