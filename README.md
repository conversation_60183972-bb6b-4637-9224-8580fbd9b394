# HLS Validator

A comprehensive CLI tool to validate HLS (HTTP Live Streaming) streams for compliance and quality issues.

> WARNING: this project has been completely generated by AugmentCode AI, it might be bugged or incomplete

## Features

- **Remote Stream Support**: Validate HLS streams from any HTTP/HTTPS URL
- **Batch Validation**: Validate multiple streams from a file list
- **Comprehensive Validation**:
  - Segment duration consistency across different quality levels
  - Declared vs actual bitrate validation
  - I-frame detection at segment boundaries using PyAV (libav)
  - TS packet structure validation
  - Cross-playlist consistency checks
  - **PTS Analysis**: Advanced timing validation including:
    - Overlapping PTS detection in audio/video streams
    - PTS gap detection for missing frames/packets
    - Audio/video synchronization analysis
    - Drift measurement and sync issue detection
- **Multiple Output Formats**: Table, JSON, and summary formats with verbose options
- **Professional Logging**: Configurable logging with file output support
- **Robust Error Handling**: Detailed error reporting and graceful failure handling

## Requirements

- Python 3.9+
- PyAV (libav) - automatically installed via Poetry
- Poetry (for dependency management)

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd hlsvalidator
```

2. Install dependencies using Poetry:
```bash
poetry install
```

3. PyAV will be automatically installed with the project dependencies. No additional system dependencies are required.

## Usage

### Basic Usage

```bash
# Validate a single HLS stream
poetry run hlsvalidator https://example.com/playlist.m3u8

# Validate multiple streams from a file
poetry run hlsvalidator --list urls.txt

# Or using the module
poetry run python -m hlsvalidator.cli https://example.com/playlist.m3u8
```

### Command Line Options

```bash
poetry run hlsvalidator [OPTIONS] [URL]

Options:
  -t, --timeout INTEGER    Request timeout in seconds [default: 30]
  -f, --format TEXT       Output format: table, json, or summary [default: table]
  -o, --output PATH       Output file path (optional)
  -v, --verbose           Enable verbose logging and detailed warnings
  -l, --list PATH         File containing list of URLs to validate (one per line)
  --check-pts             Check for overlapping PTS and audio/video sync issues
  --no-color              Disable colored output
  --help                  Show help message
```

### URL List File Format

When using `--list`, create a text file with one URL per line:
```
# Comments are supported (lines starting with #)
https://example.com/stream1/master.m3u8
https://example.com/stream2/master.m3u8
https://example.com/stream3/master.m3u8
```

### Examples

#### Single Stream Validation
```bash
# Basic validation with table output
poetry run hlsvalidator https://example.com/master.m3u8

# JSON output for programmatic use
poetry run hlsvalidator -f json https://example.com/playlist.m3u8

# Verbose output with detailed warnings
poetry run hlsvalidator -v https://example.com/playlist.m3u8

# Summary format for quick overview
poetry run hlsvalidator -f summary https://example.com/master.m3u8

# Advanced PTS timing analysis
poetry run hlsvalidator --check-pts https://example.com/stream.m3u8

# Save results to file
poetry run hlsvalidator -o results.json -f json https://example.com/stream.m3u8
```

#### Batch Validation
```bash
# Validate multiple streams from a file
poetry run hlsvalidator --list stream-urls.txt

# Batch validation with PTS checking
poetry run hlsvalidator --list stream-urls.txt --check-pts

# Batch validation with JSON output
poetry run hlsvalidator --list stream-urls.txt -f json -o batch-results.json

# Verbose batch validation
poetry run hlsvalidator --list stream-urls.txt -v
```

#### Advanced Analysis
```bash
# Complete analysis with all features
poetry run hlsvalidator --check-pts -v -f json -o complete-analysis.json https://example.com/master.m3u8

# Quick batch check for sync issues
poetry run hlsvalidator --list problematic-streams.txt --check-pts -f summary
```

## Validation Features

### Segment Analysis
- **I-frame Detection**: Uses PyAV (libav) to analyze TS segments and verify they start with I-frames
- **Duration Consistency**: Ensures segments have consistent durations across quality levels
- **Size Validation**: Checks if segment sizes correlate with declared bitrates
- **PTS Analysis** (with `--check-pts`):
  - Overlapping PTS detection in video streams
  - Overlapping PTS detection in audio streams
  - PTS gap detection for missing frames or packets
  - Audio/video synchronization analysis
  - Drift measurement and timing validation

### Stream Validation
- **Bitrate Accuracy**: Compares declared bitrates with actual calculated bitrates
- **Cross-playlist Consistency**: Validates that different quality levels have the same number of segments
- **TS Structure**: Verifies MPEG-TS packet structure using PyAV analysis
- **Timing Validation**: Advanced PTS analysis for professional broadcast quality

### Audio/Video Sync Analysis
When using `--check-pts`, the validator performs detailed timing analysis:
- **Sync Issue Detection**: Identifies audio/video timing misalignment (>33ms drift)
- **Drift Statistics**: Provides maximum and average drift measurements
- **Frame-level Analysis**: Checks synchronization for individual frames
- **Professional Thresholds**: Uses broadcast-standard timing tolerances

### Batch Processing
- **Multiple Stream Validation**: Process hundreds of streams from a single file
- **Progress Tracking**: Real-time progress indication for batch operations
- **Summary Statistics**: Overall validation results across all streams
- **Selective Processing**: Skip invalid URLs and continue with valid ones

### Error Detection
- Network connectivity issues
- Malformed playlist files
- Missing or corrupted segments
- Bitrate mismatches
- Timing inconsistencies
- Invalid TS packet structures
- PTS overlaps and discontinuities
- Audio/video synchronization problems

## Output Formats

### Table Format (Default)
Provides a comprehensive view with:
- Overall validation status with color-coded results
- Error and warning tables with detailed descriptions
- Playlist summary with stream details (resolution, bitrate, segments)
- **Verbose mode** (`-v`): Shows all warnings including playlist-specific issues
- **PTS Analysis**: Detailed timing information when using `--check-pts`

### JSON Format
Machine-readable output suitable for:
- Integration with other tools and CI/CD pipelines
- Automated testing and quality assurance workflows
- Programmatic analysis and data processing
- **Batch results**: Includes summary statistics for multiple streams
- **PTS data**: Complete timing analysis results when enabled

### Summary Format
Concise text-based overview showing:
- Overall validation status for each stream
- Error and warning counts with descriptions
- Basic stream information and statistics
- **Batch summary**: Aggregated results for multiple streams
- **All warnings**: Both overall and playlist-specific warnings included

### Verbose Mode (`-v`)
Enhanced output in all formats:
- **Detailed warnings**: Shows both overall and playlist-specific warnings
- **Warning categorization**: Separates different types of issues
- **Progress indication**: Real-time status for batch operations
- **Debug information**: Additional logging for troubleshooting

## Development

### Running Tests

```bash
# Run all tests
poetry run pytest

# Run with coverage
poetry run pytest --cov=hlsvalidator

# Run specific test file
poetry run pytest tests/test_validator.py
```

### Code Quality

```bash
# Format code
poetry run black hlsvalidator tests

# Sort imports
poetry run isort hlsvalidator tests

# Run linting (if configured)
poetry run flake8 hlsvalidator
```

## Architecture

The project is structured as follows:

- `hlsvalidator/validator.py`: Core validation logic
- `hlsvalidator/ts_analyzer.py`: PyAV-based TS segment analysis
- `hlsvalidator/cli.py`: Command-line interface using Typer
- `hlsvalidator/exceptions.py`: Custom exception classes
- `hlsvalidator/logging_config.py`: Logging configuration and utilities

## Dependencies

- **typer**: Modern CLI framework
- **m3u8**: HLS playlist parsing
- **requests**: HTTP client for downloading streams
- **av (PyAV)**: Python bindings for libav/FFmpeg
- **rich**: Beautiful terminal output

## PTS Analysis Deep Dive

### What is PTS Analysis?
PTS (Presentation Time Stamp) analysis examines the timing information embedded in MPEG-TS streams to detect:

#### Overlapping PTS Issues
- **Video overlaps**: When video frames have non-increasing timestamps
- **Audio overlaps**: When audio packets have non-increasing timestamps
- **Impact**: Can cause playback stuttering or frame drops

#### PTS Gaps
- **Large video gaps**: Missing frames or encoding discontinuities
- **Large audio gaps**: Missing audio packets or silence periods
- **Detection thresholds**:
  - Video: Gaps larger than 3 frame durations
  - Audio: Gaps larger than 5 audio frame durations

#### Audio/Video Sync Analysis
- **Drift detection**: Measures timing differences between audio and video streams
- **Sync thresholds**:
  - Significant issues: >33ms drift (more than one frame at 30fps)
  - Warning threshold: >16.7ms drift (more than one frame at 60fps)
- **Professional standards**: Uses broadcast-quality timing tolerances

### When to Use PTS Analysis
- **Quality assurance**: Before distributing content to ensure smooth playback
- **Encoding validation**: Verify proper muxing of audio and video streams
- **Troubleshooting**: Diagnose playback issues reported by users
- **Compliance checking**: Ensure streams meet broadcast or streaming platform standards

### PTS Analysis Output
```json
{
  "pts_analysis": {
    "video_pts": [9090, 12690, 16290],
    "audio_pts": [8000, 10000, 12000],
    "overlapping_video_pts": [],
    "overlapping_audio_pts": [],
    "pts_gaps": [],
    "av_sync_issues": [
      {
        "video_pts": 12690,
        "audio_pts": 15660,
        "drift": 2970,
        "drift_ms": 33.0,
        "frame_index": 1
      }
    ],
    "max_av_drift_ms": 33.0,
    "avg_av_drift_ms": 16.5
  }
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

[Add your license information here]

## Troubleshooting

### PyAV Installation Issues
If you encounter issues with PyAV installation:
1. Ensure you have system development tools installed
2. On Ubuntu/Debian: `sudo apt-get install build-essential pkg-config`
3. On macOS: Install Xcode command line tools
4. PyAV will automatically use system FFmpeg libraries if available

### Network Timeouts
For slow connections, increase the timeout:
```bash
poetry run hlsvalidator -t 60 https://example.com/playlist.m3u8
```

### Memory Issues with Large Streams
The tool analyzes segments in chunks to minimize memory usage, but for very large streams, consider:
- Using summary format instead of detailed analysis
- Increasing system memory
- Processing streams in smaller batches

### Batch Validation Issues
When validating multiple streams:
- **File format**: Ensure URL list file has one URL per line
- **Invalid URLs**: The tool will skip invalid URLs and continue
- **Large batches**: Consider processing in smaller chunks for better performance
- **Progress tracking**: Use verbose mode (`-v`) to see detailed progress

### PTS Analysis Performance
PTS analysis is computationally intensive:
- **Limited scope**: Analysis is limited to first 100 packets per stream for performance
- **Memory usage**: PTS data is limited to prevent memory issues
- **Processing time**: Expect longer validation times when using `--check-pts`
- **Audio-only streams**: PTS sync analysis requires both audio and video streams

### Common Warning Explanations
- **"Segment duration varies significantly"**: Normal for the last segment in a stream
- **"Size ratio doesn't match bitrate ratio"**: Common with variable bitrate encoding
- **"Audio/video sync issues"**: May indicate encoding problems or source material issues
- **"Overlapping PTS"**: Serious timing issue that can cause playback problems

### Performance Optimization
For better performance with large-scale validation:
```bash
# Use summary format for faster processing
poetry run hlsvalidator --list urls.txt -f summary

# Skip PTS analysis for basic validation
poetry run hlsvalidator --list urls.txt

# Process in parallel (external tools)
split -l 100 urls.txt batch_
for batch in batch_*; do
  poetry run hlsvalidator --list "$batch" -f json -o "results_$batch.json" &
done
wait
```

## Quick Reference

### Most Common Commands
```bash
# Basic validation
poetry run hlsvalidator https://example.com/master.m3u8

# Batch validation
poetry run hlsvalidator --list urls.txt

# Complete analysis with timing
poetry run hlsvalidator --check-pts -v https://example.com/stream.m3u8

# JSON output for automation
poetry run hlsvalidator --list urls.txt -f json -o results.json
```

### Warning Types
| Warning | Meaning | Severity |
|---------|---------|----------|
| Duration varies significantly | Last segment is shorter (normal) | Low |
| Size ratio doesn't match bitrate | VBR encoding variation | Low |
| Overlapping PTS | Timing issue in stream | High |
| Audio/video sync issues | A/V synchronization problem | High |
| PTS gaps | Missing frames/packets | Medium |

### Exit Codes
- `0`: All streams valid
- `1`: One or more streams invalid or errors occurred

### File Formats
- **URL list**: Plain text, one URL per line, `#` for comments
- **JSON output**: Complete validation results with all data
- **Summary output**: Human-readable text format
