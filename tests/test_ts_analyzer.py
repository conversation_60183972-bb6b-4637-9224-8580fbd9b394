"""Tests for PyAV-based TS analyzer functionality."""

import pytest
from unittest.mock import Mock, patch, MagicMock
import io

from hlsvalidator.ts_analyzer import TSAnalyzer
from hlsvalidator.exceptions import TSAnalysisError


class TestTSAnalyzer:
    """Test cases for PyAV-based TSAnalyzer class."""

    def setup_method(self):
        """Setup test fixtures."""
        # Mock PyAV availability check
        with patch('av.open'):
            self.analyzer = TSAnalyzer()

    @patch('av.open')
    def test_pyav_not_available(self, mock_open):
        """Test error when PyAV is not available."""
        mock_open.side_effect = Exception("PyAV not available")

        # This should not raise an error during initialization
        # as we catch the exception in _check_av_availability
        analyzer = TSAnalyzer()

    def test_analyze_empty_segment(self):
        """Test analysis of empty segment."""
        result = self.analyzer.analyze_segment(b'')

        assert not result['valid_ts']
        assert result['packet_count'] == 0
        assert len(result['errors']) > 0
        assert "too small" in result['errors'][0]

    def test_analyze_small_segment(self):
        """Test analysis of segment smaller than TS packet."""
        small_data = b'\x47' * 100  # Less than 188 bytes
        result = self.analyzer.analyze_segment(small_data)

        assert not result['valid_ts']
        assert len(result['errors']) > 0

    @patch('av.open')
    def test_analyze_valid_segment_with_video(self, mock_av_open):
        """Test analysis of valid segment with video stream."""
        # Mock PyAV container and streams
        mock_container = Mock()
        mock_video_stream = Mock()
        mock_video_stream.type = 'video'
        mock_video_stream.codec_context = Mock()
        mock_video_stream.codec_context.name = 'h264'
        mock_video_stream.codec_context.width = 1920
        mock_video_stream.codec_context.height = 1080
        mock_video_stream.codec_context.bit_rate = 5000000
        mock_video_stream.average_rate = 25.0
        mock_video_stream.duration = 10000
        mock_video_stream.time_base = 0.001

        mock_container.streams = [mock_video_stream]
        mock_container.duration = 10000000  # microseconds
        mock_container.bit_rate = 5000000
        mock_container.size = 6250000

        mock_av_open.return_value.__enter__.return_value = mock_container

        # Mock frame analysis
        with patch.object(self.analyzer, '_check_iframe_start_pyav') as mock_iframe:
            mock_iframe.return_value = None  # Modifies result in place

            segment_data = b'\x47' * 1880  # 10 TS packets worth
            result = self.analyzer.analyze_segment(segment_data)

            assert result['valid_ts']
            assert result['has_video']
            assert len(result['video_streams']) == 1
            assert result['video_streams'][0]['codec_name'] == 'h264'
            assert result['video_streams'][0]['width'] == 1920
            assert result['video_streams'][0]['height'] == 1080

    def test_check_iframe_start_with_iframe(self):
        """Test I-frame detection when segment starts with I-frame."""
        # Mock container and video stream
        mock_container = Mock()
        mock_video_stream = Mock()
        mock_video_stream.type = 'video'
        mock_container.streams = [mock_video_stream]

        # Mock packet and frame
        mock_packet = Mock()
        mock_frame = Mock()
        mock_frame.key_frame = True
        mock_frame.pict_type = Mock()
        mock_frame.pict_type.name = 'I'

        mock_packet.decode.return_value = [mock_frame]
        mock_container.demux.return_value = [mock_packet]
        mock_container.seek = Mock()

        result = {'has_video': True, 'warnings': []}
        self.analyzer._check_iframe_start_pyav(mock_container, result)

        assert result['starts_with_iframe'] is True
        assert len(result['warnings']) == 0

    def test_check_iframe_start_without_iframe(self):
        """Test I-frame detection when segment doesn't start with I-frame."""
        # Mock container and video stream
        mock_container = Mock()
        mock_video_stream = Mock()
        mock_video_stream.type = 'video'
        mock_container.streams = [mock_video_stream]

        # Mock packet and frame (P-frame)
        mock_packet = Mock()
        mock_frame = Mock()
        mock_frame.key_frame = False
        mock_frame.pict_type = Mock()
        mock_frame.pict_type.name = 'P'

        mock_packet.decode.return_value = [mock_frame]
        mock_container.demux.return_value = [mock_packet]
        mock_container.seek = Mock()

        result = {'has_video': True, 'warnings': []}
        self.analyzer._check_iframe_start_pyav(mock_container, result)

        assert result['starts_with_iframe'] is False
        assert len(result['warnings']) > 0
        assert "does not start with I-frame" in result['warnings'][0]

    @patch('av.open')
    def test_pyav_error_handling(self, mock_av_open):
        """Test handling of PyAV errors."""
        import av
        mock_av_open.side_effect = av.FFmpegError(-1, 'Invalid data')

        segment_data = b'\x47' * 1880
        result = self.analyzer.analyze_segment(segment_data)

        assert not result['valid_ts']
        assert len(result['errors']) > 0
        assert "PyAV analysis failed" in result['errors'][0]
