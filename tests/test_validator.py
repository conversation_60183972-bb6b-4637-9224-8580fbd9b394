"""Tests for HLS validator functionality."""

import pytest
from unittest.mock import Mo<PERSON>, patch, MagicMock
import requests

from hlsvalidator.validator import HLSValidator
from hlsvalidator.exceptions import PlaylistLoadError, NetworkError


class TestHLSValidator:
    """Test cases for HLSValidator class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.validator = HLSValidator(timeout=10)
    
    def test_init(self):
        """Test validator initialization."""
        assert self.validator.timeout == 10
        assert self.validator.session is not None
        assert self.validator.ts_analyzer is not None
    
    @patch('hlsvalidator.validator.requests.Session.get')
    @patch('hlsvalidator.validator.m3u8.loads')
    def test_load_playlist_success(self, mock_m3u8_loads, mock_get):
        """Test successful playlist loading."""
        # Mock response
        mock_response = Mock()
        mock_response.text = "#EXTM3U\n#EXT-X-VERSION:3\n"
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Mock m3u8 parsing
        mock_playlist = Mock()
        mock_m3u8_loads.return_value = mock_playlist
        
        result = self.validator._load_playlist("http://example.com/playlist.m3u8")
        
        assert result == mock_playlist
        mock_get.assert_called_once()
        mock_m3u8_loads.assert_called_once()
    
    @patch('hlsvalidator.validator.requests.Session.get')
    def test_load_playlist_network_error(self, mock_get):
        """Test playlist loading with network error."""
        mock_get.side_effect = requests.RequestException("Network error")
        
        with pytest.raises(PlaylistLoadError):
            self.validator._load_playlist("http://example.com/playlist.m3u8")
    
    @patch('hlsvalidator.validator.requests.Session.get')
    @patch('hlsvalidator.validator.m3u8.loads')
    def test_load_playlist_parse_error(self, mock_m3u8_loads, mock_get):
        """Test playlist loading with parse error."""
        # Mock response
        mock_response = Mock()
        mock_response.text = "invalid playlist"
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Mock m3u8 parsing error
        mock_m3u8_loads.side_effect = Exception("Parse error")
        
        with pytest.raises(PlaylistLoadError):
            self.validator._load_playlist("http://example.com/playlist.m3u8")
    
    def test_validate_stream_invalid_url(self):
        """Test validation with invalid URL format."""
        # This would be handled by CLI, but test the validator directly
        result = self.validator.validate_stream("invalid-url")
        
        assert not result['valid']
        assert len(result['errors']) > 0
    
    @patch('hlsvalidator.validator.HLSValidator._load_playlist')
    def test_validate_single_playlist(self, mock_load_playlist):
        """Test validation of single media playlist."""
        # Mock playlist
        mock_playlist = Mock()
        mock_playlist.is_variant = False
        mock_playlist.segments = []
        mock_load_playlist.return_value = mock_playlist
        
        result = self.validator.validate_stream("http://example.com/playlist.m3u8")
        
        assert 'playlists' in result
        assert len(result['playlists']) == 1
    
    @patch('hlsvalidator.validator.HLSValidator._load_playlist')
    @patch('hlsvalidator.validator.HLSValidator._validate_media_playlist')
    def test_validate_master_playlist(self, mock_validate_media, mock_load_playlist):
        """Test validation of master playlist with variants."""
        # Mock master playlist
        mock_master = Mock()
        mock_master.is_variant = True
        
        # Mock variant playlists
        mock_variant1 = Mock()
        mock_variant1.uri = "variant1.m3u8"
        mock_variant2 = Mock()
        mock_variant2.uri = "variant2.m3u8"
        mock_master.playlists = [mock_variant1, mock_variant2]
        
        mock_load_playlist.return_value = mock_master
        
        # Mock validation results
        mock_validate_media.return_value = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'segments': []
        }
        
        result = self.validator.validate_stream("http://example.com/master.m3u8")
        
        assert len(result['playlists']) == 2
        assert mock_validate_media.call_count == 2
    
    @patch('hlsvalidator.validator.requests.Session.head')
    @patch('hlsvalidator.validator.requests.Session.get')
    def test_validate_segment_success(self, mock_get, mock_head):
        """Test successful segment validation."""
        # Mock HEAD response for size
        mock_head_response = Mock()
        mock_head_response.headers = {'content-length': '1024000'}
        mock_head_response.raise_for_status.return_value = None
        mock_head.return_value = mock_head_response
        
        # Mock GET response for TS analysis
        mock_get_response = Mock()
        mock_get_response.status_code = 206
        mock_get_response.content = b'\x47' * 8192  # Mock TS data
        mock_get.return_value = mock_get_response
        
        # Mock TS analyzer
        with patch.object(self.validator.ts_analyzer, 'analyze_segment') as mock_analyze:
            mock_analyze.return_value = {
                'valid_ts': True,
                'has_video': True,
                'has_audio': False,
                'starts_with_iframe': True,
                'video_streams': [{'codec_name': 'h264'}],
                'audio_streams': [],
                'packet_count': 43,
                'valid_packets': 43,
                'duration': '10.0',
                'bitrate': '5000000',
                'errors': [],
                'warnings': []
            }
            
            # Mock segment info
            mock_segment = Mock()
            mock_segment.duration = 10.0
            
            result = self.validator._validate_segment(
                "http://example.com/segment.ts", 
                mock_segment, 
                0
            )
            
            assert result['valid']
            assert result['size_bytes'] == 1024000
            assert result['starts_with_iframe']
            assert 'ts_analysis' in result
    
    def test_check_segment_duration_consistency(self):
        """Test segment duration consistency checking."""
        playlist_result = {
            'segment_durations': [10.0, 10.1, 9.9, 10.0, 10.2],
            'warnings': []
        }
        
        self.validator._check_segment_duration_consistency(playlist_result)
        
        # Should not add warnings for small variations
        assert len(playlist_result['warnings']) == 0
        
        # Test with large variation
        playlist_result['segment_durations'] = [10.0, 12.0, 10.0]
        playlist_result['warnings'] = []
        
        self.validator._check_segment_duration_consistency(playlist_result)
        
        # Should add warning for large variation
        assert len(playlist_result['warnings']) > 0
    
    def test_cross_validate_playlists_segment_count(self):
        """Test cross-validation of segment counts."""
        playlists = [
            {'segments': [1, 2, 3], 'segment_durations': [10, 10, 10]},
            {'segments': [1, 2], 'segment_durations': [10, 10]}  # Different count
        ]
        
        results = {'errors': [], 'warnings': []}
        
        self.validator._cross_validate_playlists(playlists, results)
        
        assert len(results['errors']) > 0
        assert "Inconsistent segment counts" in results['errors'][0]
    
    def test_validate_bitrates(self):
        """Test bitrate validation."""
        playlists = [
            {
                'declared_bandwidth': 1000000,
                'segments': [
                    {'size_bytes': 125000, 'duration': 10.0},  # 100kbps actual
                    {'size_bytes': 125000, 'duration': 10.0}
                ]
            }
        ]
        
        results = {'warnings': []}
        
        self.validator._validate_bitrates(playlists, results)
        
        # Should warn about bitrate mismatch (declared 1Mbps vs actual ~100kbps)
        assert len(results['warnings']) > 0
